 import React from 'react';
 import { Provider } from 'react-redux';
 import { ThemeProvider } from '@mui/styles';
 import { createTheme } from '@mui/material/styles';
 import AppContainer from './AppContainer';
 import store from './store';

const theme = createTheme();

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <AppContainer />
      </ThemeProvider>
    </Provider>
  );
}

export default App;
