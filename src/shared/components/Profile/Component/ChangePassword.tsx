import React from 'react'
import { changePasswordModalStyle } from '../style'

// Material UI Imports
import {
    Grid,
    Box,
    Dialog,
    Button,
    DialogTitle,
    DialogActions,
    DialogContent,
    IconButton,
    // TextField,
    InputLabel,
    OutlinedInput,
    InputAdornment
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close'
import Visibility from '@mui/icons-material/Visibility'
import VisibilityOff from '@mui/icons-material/VisibilityOff'
import ErrorIcon from '@mui/icons-material/Error'
import MsgModal from './MsgModal'
import { getIndexDb } from '../../../../utils/CoreApi';
import { useDispatch } from 'react-redux';
import { updatePassword } from '../../../reducers/LoginSlice';
import { setIsRequesting } from '../../../reducers/FranchiseSlice';
// import { RootState } from '../../../reducers/rootReducer';
// import { StorageService } from '../../../../Utils'

interface IProps {
    visible: boolean,
    userId: string,
    // url: string | undefined,
    onClose: () => void;
    // setLoading: any
}

interface textFieldProps {
    label: string,
    value: string,
    classes: any,
    note?: any,
    id: string
    error: boolean,
    errorMsg: string,
    onChange: (value: any, prop: any) => void;
}

export const ChangePassword:
    React.FC<IProps> = (props: IProps)=> {
        const dispatch = useDispatch()

        React.useEffect(() => {
            getIndexDb('config', 'password').then((r) => {
                setPwValidator(r.result)
            }).catch((error) => {
                console.error('Error fetching password config:', error);
            })
        }, [])

        const {
            visible,
            onClose
        } = props;
        const classes = changePasswordModalStyle()

        // Password values handling
        const handleTextFieldChange = (e: any, prop: any) => {
            const { value } = e.target

            setPasswordValues({ ...passwordValues, [prop]: value })

            const regex = new RegExp(pwValidator.regex)

            if (prop === 'newPassword') {
                if (!regex.test(value)) {
                    setNerror({ message: 'Invalid Input', error: true })
                } else {
                    if (value === passwordValues.confirmPassword) {
                        setNerror({ message: '', error: false })
                        setCerror({ message: '', error: false })
                    } else {
                        setCerror({ message: 'Password doesn’t match', error: true })
                        setNerror({ message: '', error: true })
                    }
                }
            } else if (prop === 'confirmPassword') {
                if (!regex.test(value)) {
                    setCerror({ message: 'Invalid Input', error: true })
                } else {
                    if (!regex.test(value)) {
                        setCerror({ message: 'Invalid Input', error: true })
                    } else {
                        if (value === passwordValues.newPassword) {
                            setNerror({ message: '', error: false })
                            setCerror({ message: '', error: false })
                        } else {
                            setCerror({ message: 'Password doesn’t match', error: true })
                            setNerror({ message: '', error: true })
                        }
                    }
                }
            } else if (prop === 'oldPassword') {
                if (value === '') {
                    setOerror({ message: 'Invalid Input', error: true })
                } else {
                    setOerror({ message: '', error: false })
                }
            }
        }

        // Password values reset default
        const resetPasswordValues = () => {
            setPasswordValues({
                oldPassword: '',
                newPassword: '',
                confirmPassword: ''
            })
            setOerror({
                ...oError,
                error: false
            })
            setNerror({
                ...nError,
                error: false
            })
            setCerror({
                ...cError,
                error: false
            })

            onClose()
        }

        const fetchChange = async () => {
            // Declare password values
            const { oldPassword, newPassword, confirmPassword } = passwordValues

            let constructParams = {
                old_password: oldPassword,
                new_password: newPassword,
                conf_password: confirmPassword
            }

            if (oldPassword === '') {
                setOerror({ message: 'Invalid Input', error: true })

                return 
            } else {
                setOerror({ message: '', error: false })
            }

            const changePassword = dispatch(updatePassword(constructParams))
            Promise.all([changePassword])
                .then((completed: any) => {
                    dispatch(setIsRequesting(false))
                    if(completed){
                        for (let index = 0; index < completed.length; index++) {
                            if(completed[index]){
                                setModalDone({
                                    title: 'Change Password Success',
                                    message: 'You have successfully changed your password.',
                                    visible: true
                                })
                                setPasswordValues({
                                    oldPassword: '',
                                    newPassword: '',
                                    confirmPassword: ''
                                })
                                onClose();
                            }
                        }
                    }
                    // let jsondata = await data.json();

                    // if (!jsondata.error) {
                    //     setModalDone({
                    //         title: 'Change Password Success',
                    //         message: 'You have successfully changed your password.',
                    //         visible: true
                    //     })
                    //     setPasswordValues({
                    //         oldPassword: '',
                    //         newPassword: '',
                    //         confirmPassword: ''
                    //     })
                    //     onClose();
                    // } else {
                    //     setModalDone({
                    //         title: 'Error',
                    //         message: 'Something went wrong. Your password has not been changed.',
                    //         visible: true
                    //     })
                    // }

                    // props.setLoading(false)
                    
                    
                }).catch((err: any) => {
                    dispatch(setIsRequesting(false))
                    if(err.response){
                        if(err.message.includes('UM21')){
                            setOerror({ message: 'Incorrect Password', error: true })
                        }
                            setModalDone({
                                title: 'Error',
                                message: 'Something went wrong. Your password has not been changed.',
                                visible: true
                            })

                        // if(err.message.includes('UM90')){
                        //     setModalDone({
                        //         title: 'Error',
                        //         message: 'You may not reuse your 3 previous passwords.',
                        //         visible: true
                        //     })
                        // }else if(err.message.includes('UM21')){
                        //     setOerror({ message: 'Incorrect Password', error: true })
                        //     setModalDone({
                        //         title: 'Error',
                        //         message: 'Old password is incorrect.',
                        //         visible: true
                        //     })
                        // }else{
                        //     setModalDone({
                        //         title: 'Error',
                        //         message: 'Something went wrong. Your password has not been changed.',
                        //         visible: true
                        //     })
                        // }
                        
                    }
                    
                })

        }

        const [passwordValues, setPasswordValues] = React.useState({
            oldPassword: '',
            newPassword: '',
            confirmPassword: ''
        })
        const [oError, setOerror] = React.useState<any>({
            message: '',
            error: false
        })
        const [nError, setNerror] = React.useState<any>({
            message: '',
            error: null
        })
        const [cError, setCerror] = React.useState<any>({
            message: '',
            error: null
        })

        const [modalDone, setModalDone] = React.useState<any>({
            message: '',
            title: '',
            visible: false
        })

        const [pwValidator, setPwValidator] = React.useState<any>({
            regex: '',
            character: []
        })

        const handleDoneClose = () => {
            setModalDone({
                message: '',
                title: '',
                visible: false
            })
        }


        return (
            <>
                <Dialog
                    open={visible}
                    onClose={resetPasswordValues}
                    aria-labelledby="form-dialog-title"
                    maxWidth="sm"
                    className={classes.dialog}>
                    {/* Modal Content Area */}
                    <Grid
                        container
                        className={classes.container}
                        justifyContent="flex-start"
                        alignItems="flex-start"
                        direction="column">
                        <Grid item xs>
                        <DialogTitle
                            className={classes.dialogTitle}
                            id="form-dialog-title" >
                            <Box fontWeight={700}>Change Password</Box>
                        </DialogTitle>
                        <IconButton
                            data-cy="close-changepassword-modal"
                            aria-label="close"
                            className={classes.closeButton}
                            onClick={resetPasswordValues}
                            style={{ position: 'absolute', top: 16, right: 16 }}>
                            <CloseIcon />
                        </IconButton>
                        </Grid>
                        <Grid
                            item
                            xs
                            className={classes.dialogContentContainer}>
                            <DialogContent>
                                <Grid
                                    container
                                    spacing={2}
                                    justifyContent="space-between"
                                    alignItems="flex-start">
                                    <Grid item xs={12}>
                                        <TextFieldWithEyeIcon
                                            id='oldPassword'
                                            error={oError.error}
                                            errorMsg={oError.message}
                                            label='Enter your old password'
                                            value={passwordValues.oldPassword}
                                            onChange={handleTextFieldChange}
                                            classes={classes} />
                                    </Grid>
                                    <Grid item xs={12}>
                                        <TextFieldWithEyeIcon
                                            id='newPassword'
                                            error={nError.error}
                                            errorMsg={nError.message}
                                            label='Enter your new password'
                                            note={
                                                <>
                                                    <span style={{display: 'block', fontSize: 12, color: '#797F92', marginBottom: 5}}>Please update your password to continue.</span>
                                                    <ul style={{display: 'block', fontSize: 12, color: '#797F92', paddingLeft: 15, listStyle: 'disc'}}>
                                                        <li>Must be <strong>{pwValidator.min_length} min to {pwValidator.max_length} max characters</strong></li>
                                                        {
                                                            pwValidator.character.map((e: any, key: any) => 
                                                                e === 'number' ? 
                                                                    <li key={key}>Must have <strong>letters and numbers</strong></li> :
                                                                e === 'uppercase_letter' ?
                                                                    <li>Must have <strong>at least 1 upper case</strong></li> :
                                                                e === 'special_character' ?
                                                                    <li>Must have <strong>at least 1 special</strong></li> : ''
                                                            )
                                                        }
                                                    </ul>
                                                </>
                                            }
                                            value={passwordValues.newPassword}
                                            onChange={handleTextFieldChange}
                                            classes={classes} />
                                    </Grid>
                                    <Grid item xs={12}>
                                        <TextFieldWithEyeIcon
                                            id='confirmPassword'
                                            error={cError.error}
                                            errorMsg={cError.message}
                                            label='Confirm your new password'
                                            value={passwordValues.confirmPassword}
                                            onChange={handleTextFieldChange}
                                            classes={classes} />
                                    </Grid>
                                </Grid>
                            </DialogContent>
                            <DialogActions className={classes.dialogAction}>
                                 <Grid
                                     container
                                     spacing={1}
                                     justifyContent="center"
                                     alignItems="center">
                                    <Grid item xs={6}>
                                         <Button
                                             data-cy="cancel-btn-changepassword-modal"
                                             onClick={resetPasswordValues}
                                             sx={{
                                                 margin: '0 5px',
                                                 minHeight: '42px',
                                                 minWidth: 140,
                                                 background: '#FFFFFF',
                                                 border: '.5px solid #3AB77D',
                                                 borderRadius: '4px',
                                                 color: '#3AB77D',
                                                 marginRight: 1,
                                                 '&:hover': {
                                                     background: '#FFFFFF'
                                                 }
                                             }}
                                             >Cancel</Button>
                                    </Grid>
                                    <Grid item xs={6}>
                                         <Button
                                             disabled={
                                                 oError.error !== null && cError.error !== null && nError.error !== null &&
                                                 !oError.error && !cError.error && !nError.error ? false : true
                                             }
                                             onClick={fetchChange}
                                             sx={{
                                                 margin: '0 10px',
                                                 minHeight: '42px',
                                                 minWidth: 140,
                                                 background: '#3AB77D',
                                                 color: '#FFFFFF',
                                                 '&:hover': {
                                                     background: '#3AB77D'
                                                 },
                                                 '&:disabled':{
                                                     background: '#e0e0e0'
                                                 }
                                             }}
                                             variant={'contained'}
                                             data-cy="change-btn-changepassword-modal"
                                             >Change</Button>
                                    </Grid>
                                </Grid>
                            </DialogActions>
                        </Grid>
                    </Grid>
                </Dialog>

                    <MsgModal
                        isModalOpen={modalDone.visible}
                        onClose={handleDoneClose}
                        title={modalDone.title}
                        message={modalDone.message}
                    />
            </>
        )
    }



const TextFieldWithEyeIcon: React.FC<textFieldProps> = (options: textFieldProps)=> {
    const {
        classes,
        label,
        onChange,
        value,
        note,
        id,
        error,
        errorMsg
    } = options

    const [type, setType] = React.useState<string>('password')

    const handleClickShowPassword = () => {
        if (type === 'password') {
            setType('text')
        } else {
            setType('password')
        }
    }

    return (
        <>
            <InputLabel
                className={classes.inputLabel}
                htmlFor="role_id">{label}</InputLabel>
            {note && note}
            <OutlinedInput
                id={id}
                error={error}
                type={type}
                onChange={(e) => onChange(e, id)}
                value={value}
                className={classes.inputTextField}
                // helperText="Incorrect entry."
                data-cy={`${id}-inputfield`}
                endAdornment={
                    <InputAdornment position="end">
                        <IconButton
                            aria-label="toggle password visibility"
                            onClick={handleClickShowPassword}>
                            {type === 'text' ? <Visibility className={classes.iconEye} /> : <VisibilityOff className={classes.iconEye} />}
                        </IconButton>
                    </InputAdornment>
                } />
            {
                error &&
                <Grid container>
                    <Grid item xs={9}>
                        <span className={classes.errorMessage}>{errorMsg}</span>
                    </Grid>
                    <Grid item xs={3} style={{ textAlign: 'right' }}>
                        <ErrorIcon className={classes.errorIcon} fontSize='small' />
                    </Grid>
                </Grid>
            }
        </>
    )
}