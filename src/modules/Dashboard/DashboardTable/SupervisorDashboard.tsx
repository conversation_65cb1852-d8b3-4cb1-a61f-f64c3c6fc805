import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../shared/reducers/rootReducer";
import FranchiseList from "../../../shared/components/FranchiseList";
import { NavLink } from "react-router-dom";
import DashboardCounter from "../DashboardCounter";
import { Dialog, DialogActions, DialogContent, DialogTitle, Grid, Typography } from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
// import { decryptCredentials } from "../../../utils/LoginService";
import { logout } from "../../../shared/reducers/LoginSlice";
import { apiURL } from "../../../utils/Environment";

interface SpecialistDashboardProps {
  token: string;
  count: number;
}

const SupervisorDashboard = (props: SpecialistDashboardProps) => {
  const dispatch = useDispatch();
  const { token, count } = props;
  //* change this
  let listUrl = `/franchising/franchise/supervisor`;
  const flex = {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
  };

  const divider = {
    display: "inline-block",
    padding: "0 20px",
  };

  const firstname = useSelector((state: RootState) => state.login.firstname);
  // const [decryptedFirstName, setDecryptedFirstName] = React.useState("");
  const userRoles = useSelector((state: RootState) => state.login.userRoles);
  const multipleRoles = Object.values(userRoles).reduce(
    (a, userRole) => a + userRole,
    0
  );

  const [date_from, setDateFrom] = React.useState<any>(null);
  const [date_to, setDateTo] = React.useState<any>(null);
  const [isMultipleRoles, setIsMultipleRoles] = React.useState(false);
  const [applyDateFilter, setApplyDateFilter] = React.useState<any>(false);
  const [viewExpiredModal, setViewExpiredModal] = React.useState(false);

  React.useEffect(() => {
    if (multipleRoles > 1) {
      setIsMultipleRoles(true);
    }
  }, [multipleRoles]);

  const handleDateFromChange = (e: any) => {
    setDateFrom(e);
    if (e && date_to) {
      setApplyDateFilter(true);
    } else {
      setApplyDateFilter(false);
    }
  };

  const handleDateToChange = (e: any) => {
    setDateTo(e);
    if (e && date_from) {
      setApplyDateFilter(true);
    } else {
      setApplyDateFilter(false);
    }
  };

  let dateFilter = (
    <Grid item xs={12} style={{ textAlign: "right" }}>
      <div style={flex}>
        <span style={divider}>
          <Typography className="CustomLabel vni-inline-block" component="p">
            Select Date from
          </Typography>
        </span>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            className="vni-date-picker"
            format="MM/dd/yyyy"
            maxDate={new Date()}
            value={date_from}
            onChange={handleDateFromChange}
            slotProps={{
              textField: {
                variant: 'outlined',
                placeholder: "mm/dd/yyyy",
              },
            }}
          />
        </LocalizationProvider>
        <span style={divider}>
          <Typography className="CustomLabel vni-inline-block" component="p">
            to
          </Typography>
        </span>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            className="vni-date-picker"
            format="MM/dd/yyyy"
            value={date_to}
            minDate={date_from ?? undefined}
            maxDate={new Date()}
            onChange={handleDateToChange}
            slotProps={{
              textField: {
                variant: 'outlined',
                placeholder: "mm/dd/yyyy",
              },
            }}
          />
        </LocalizationProvider>
      </div>
    </Grid>
  );

  // React.useEffect(() => {
  //   if (firstname) {
  //     const decryptedName = decryptCredentials(firstname);
  //     setDecryptedFirstName(decryptedName);
  //   }
  // }, [firstname]);

  const checkToken = (accessToken: any) => {
    let URL = apiURL.userManagement + '/underwriting/user/check'

    return fetch(URL, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + accessToken,
        }}).then(async (res: any) => {
            if (!res.ok) {
                let respjson = await res.json()
                let errormessage = respjson.error.message
                throw Error(errormessage)
            }
            return res ? res.json() : null
        })
  }

  const isTokenValid = async () => {
    let isValid = true,
    isPmValid = true,
    isApiValid = true;
    
    if (!localStorage.getItem('api_token') || !localStorage.getItem('pm_token')) {
      isValid = false;
    } else if (
      localStorage.getItem('api_token') &&
      localStorage.getItem('pm_token')
    ) {
      try {
        let api_token = await checkToken(localStorage.getItem('api_token'));
        let pm_token = await checkToken(localStorage.getItem('pm_token'));
        if (!api_token.status) {
          isApiValid = false;
        }
        if (!pm_token.status) {
          isPmValid = false;
        }
      } catch (error) {
        console.log(error, 'error');
        isPmValid = false;
        isApiValid = false;
      }
    }

    console.log('TOKEN VALIDITY', isValid, isPmValid, isApiValid);
    if (!isValid || !isPmValid || !isApiValid) {
      // console.log('INVALID TOKEN');
      // var a = '../customer-care/index.html#/franchising' + '/invalidSession';
      // window.location.href = a;
      //onLogout();
      setViewExpiredModal(true);
    }
  };

  React.useEffect(() => {
    isTokenValid()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token])

  return (
    <React.Fragment>
      <h1 className="vni-font-bold vni-text-3xl">
        {/* <span>Welcome, {decryptedFirstName}!</span> */}
        <span>Welcome, {firstname}!</span>
      </h1>
      <div className="vni-p-br">
        <hr />
        <>
          <div className="vni-font-bold-title">
            Franchise Application Form Supervisor
          </div>

          <DashboardCounter
            isMultipleRole={isMultipleRoles}
            userRole="supervisor"
          />
          <Grid
            container
            justifyContent={"flex-end"}
            alignItems="center"
            style={{ padding: "20px 0" }}
          >
            {dateFilter}
          </Grid>
          <div className="vni-table-container">
            <div className="vni-p-3 vni-font-bold">
              Franchise Summary Status
            </div>
            {token ? (
              <FranchiseList
                isMultipleRole={isMultipleRoles}
                userRole="supervisor"
                applyFilter={applyDateFilter}
                dateFrom={date_from}
                dateTo={date_to}
              />
            ) : null}

            <div className="vni-p-5 vni-text-right vni-flex-0">
              {count > 10 ? (
                <NavLink
                  className="vni-block"
                  data-cy="view-all-franchise"
                  to={listUrl}
                >
                  View All
                </NavLink>
              ) : (
                <></>
              )}
            </div>
          </div>
        </>
      </div>

      {viewExpiredModal === true && (

        <Dialog 
            id="session-modal-expired"
            maxWidth='xs'
            open={viewExpiredModal}
            onClose={() => {
            dispatch(logout())
            window.location.replace('../index.html#/')
            }}
            className="vni-m-auto"
        >
            <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
              <h2 className="title">Session Expired</h2>
            </DialogTitle>
            <DialogContent>
              <p className="vni-mb-8">
              Your account has been logged out. Please try logging in again.
              </p>
            </DialogContent>
            <DialogActions className="vni-flex d-flex-center">
              <button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
                  dispatch(logout())
                  window.location.replace('../index.html#/')

                  }}>Okay</button>
            </DialogActions>
        </Dialog>
        )}
    </React.Fragment>
  );
};

export default SupervisorDashboard;
